{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\nimport type { Database } from './database.types'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables')\n}\n\n// Singleton browser client to avoid multiple instances\nlet browserClient: ReturnType<typeof createBrowserClient<Database>> | null = null\n\n// Browser client for client-side operations with proper auth handling\nexport function createSupabaseClient() {\n  if (typeof window === 'undefined') {\n    // Server-side: create a new client each time\n    return createClient<Database>(supabaseUrl, supabaseAnonKey)\n  }\n\n  // Client-side: use singleton pattern\n  if (!browserClient) {\n    browserClient = createBrowserClient<Database>(supabaseUrl, supabase<PERSON><PERSON><PERSON><PERSON>, {\n      auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n      },\n      isSingleton: true\n    })\n  }\n\n  return browserClient\n}\n\n// For server-side operations\nexport function createSupabaseServerClient() {\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY\n\n  if (!serviceRoleKey) {\n    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')\n  }\n\n  return createClient<Database>(\n    supabaseUrl,\n    serviceRoleKey,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAIoB;AAJpB;AACA;AAAA;;;AAGA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAEA,uDAAuD;AACvD,IAAI,gBAAyE;AAGtE,SAAS;IACd,uCAAmC;;IAGnC;IAEA,qCAAqC;IACrC,IAAI,CAAC,eAAe;QAClB,gBAAgB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAY,aAAa,iBAAiB;YAC1E,MAAM;gBACJ,kBAAkB;gBAClB,gBAAgB;gBAChB,oBAAoB;YACtB;YACA,aAAa;QACf;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;IAE5D,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAChB,aACA,gBACA;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState, useCallback } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport type { Profile } from '@/lib/database.types'\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  loading: boolean\n  error: string | null\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n  clearError: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const supabase = createSupabaseClient()\n\n  const clearError = useCallback(() => {\n    setError(null)\n  }, [])\n\n  const fetchProfile = useCallback(async (userId: string): Promise<Profile | null> => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error.message || error)\n        setError(`Failed to load profile: ${error.message}`)\n        return null\n      }\n\n      return data as Profile\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n      setError('Failed to load profile')\n      return null\n    }\n  }, [supabase])\n\n  const createProfile = useCallback(async (user: User): Promise<Profile | null> => {\n    try {\n      const profileData = {\n        id: user.id,\n        email: user.email!,\n        name: user.user_metadata?.name || user.email!.split('@')[0],\n        role: (user.user_metadata?.role as 'manager' | 'worker') || 'worker'\n      }\n\n      const { data, error } = await supabase\n        .from('profiles')\n        .insert(profileData)\n        .select()\n        .single()\n\n      if (error) {\n        console.error('Error creating profile:', error)\n        setError(`Failed to create profile: ${error.message}`)\n        return null\n      }\n\n      return data as Profile\n    } catch (error) {\n      console.error('Error creating profile:', error)\n      setError('Failed to create profile')\n      return null\n    }\n  }, [supabase])\n\n  const refreshProfile = useCallback(async () => {\n    if (!user) return\n\n    setError(null)\n    let profileData = await fetchProfile(user.id)\n\n    // If profile doesn't exist, create it\n    if (!profileData) {\n      profileData = await createProfile(user)\n    }\n\n    setProfile(profileData)\n  }, [user, fetchProfile, createProfile])\n\n  useEffect(() => {\n    let mounted = true\n\n    const getSession = async () => {\n      try {\n        setError(null)\n        const { data: { session }, error } = await supabase.auth.getSession()\n\n        if (error) {\n          console.error('Error getting session:', error)\n          if (mounted) {\n            setError(`Session error: ${error.message}`)\n            setLoading(false)\n          }\n          return\n        }\n\n        if (mounted) {\n          setUser(session?.user ?? null)\n\n          if (session?.user) {\n            let profileData = await fetchProfile(session.user.id)\n            // If profile doesn't exist, create it\n            if (!profileData) {\n              profileData = await createProfile(session.user)\n            }\n            setProfile(profileData)\n          } else {\n            setProfile(null)\n          }\n\n          setLoading(false)\n        }\n      } catch (error) {\n        console.error('Error in getSession:', error)\n        if (mounted) {\n          setError('Failed to initialize session')\n          setLoading(false)\n        }\n      }\n    }\n\n    getSession()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (!mounted) return\n\n        console.log('Auth state changed:', event, session?.user?.email)\n\n        setUser(session?.user ?? null)\n        setError(null)\n\n        if (session?.user) {\n          let profileData = await fetchProfile(session.user.id)\n          // If profile doesn't exist, create it\n          if (!profileData) {\n            profileData = await createProfile(session.user)\n          }\n          setProfile(profileData)\n        } else {\n          setProfile(null)\n        }\n\n        setLoading(false)\n      }\n    )\n\n    return () => {\n      mounted = false\n      subscription.unsubscribe()\n    }\n  }, [fetchProfile, createProfile])\n\n  const signOut = useCallback(async () => {\n    try {\n      setError(null)\n      const { error } = await supabase.auth.signOut()\n      if (error) {\n        console.error('Error signing out:', error)\n        setError(`Sign out failed: ${error.message}`)\n      } else {\n        setUser(null)\n        setProfile(null)\n      }\n    } catch (error) {\n      console.error('Error signing out:', error)\n      setError('Sign out failed')\n    }\n  }, [supabase])\n\n  const value = {\n    user,\n    profile,\n    loading,\n    error,\n    signOut,\n    refreshProfile,\n    clearError\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,SAAS;QACX;+CAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACtC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,2BAA2B,MAAM,OAAO,IAAI;oBAC1D,SAAS,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;oBACnD,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;gBACT,OAAO;YACT;QACF;iDAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACvC,IAAI;gBACF,MAAM,cAAc;oBAClB,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,aAAa,EAAE,QAAQ,KAAK,KAAK,CAAE,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC3D,MAAM,AAAC,KAAK,aAAa,EAAE,QAAiC;gBAC9D;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;gBAET,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,SAAS,CAAC,0BAA0B,EAAE,MAAM,OAAO,EAAE;oBACrD,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;gBACT,OAAO;YACT;QACF;kDAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACjC,IAAI,CAAC,MAAM;YAEX,SAAS;YACT,IAAI,cAAc,MAAM,aAAa,KAAK,EAAE;YAE5C,sCAAsC;YACtC,IAAI,CAAC,aAAa;gBAChB,cAAc,MAAM,cAAc;YACpC;YAEA,WAAW;QACb;mDAAG;QAAC;QAAM;QAAc;KAAc;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;YAEd,MAAM;qDAAa;oBACjB,IAAI;wBACF,SAAS;wBACT,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBAEnE,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,IAAI,SAAS;gCACX,SAAS,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;gCAC1C,WAAW;4BACb;4BACA;wBACF;wBAEA,IAAI,SAAS;4BACX,QAAQ,SAAS,QAAQ;4BAEzB,IAAI,SAAS,MAAM;gCACjB,IAAI,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;gCACpD,sCAAsC;gCACtC,IAAI,CAAC,aAAa;oCAChB,cAAc,MAAM,cAAc,QAAQ,IAAI;gCAChD;gCACA,WAAW;4BACb,OAAO;gCACL,WAAW;4BACb;4BAEA,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,IAAI,SAAS;4BACX,SAAS;4BACT,WAAW;wBACb;oBACF;gBACF;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI,CAAC,SAAS;oBAEd,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;oBAEzD,QAAQ,SAAS,QAAQ;oBACzB,SAAS;oBAET,IAAI,SAAS,MAAM;wBACjB,IAAI,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;wBACpD,sCAAsC;wBACtC,IAAI,CAAC,aAAa;4BAChB,cAAc,MAAM,cAAc,QAAQ,IAAI;wBAChD;wBACA,WAAW;oBACb,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO;oBACL,UAAU;oBACV,aAAa,WAAW;gBAC1B;;QACF;iCAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC1B,IAAI;gBACF,SAAS;gBACT,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAC7C,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,sBAAsB;oBACpC,SAAS,CAAC,iBAAiB,EAAE,MAAM,OAAO,EAAE;gBAC9C,OAAO;oBACL,QAAQ;oBACR,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,SAAS;YACX;QACF;4CAAG;QAAC;KAAS;IAEb,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAtLgB;KAAA;AAwLT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}