export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          name: string
          role: 'manager' | 'worker'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          role: 'manager' | 'worker'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'manager' | 'worker'
          created_at?: string
          updated_at?: string
        }
      }
      scaffolding_reports: {
        Row: {
          id: string
          user_id: string
          system_line: string
          short_iso_number: string
          start_date: string
          finish_date: string
          status: 'ongoing' | 'finished'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          system_line: string
          short_iso_number: string
          start_date: string
          finish_date: string
          status: 'ongoing' | 'finished'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          system_line?: string
          short_iso_number?: string
          start_date?: string
          finish_date?: string
          status?: 'ongoing' | 'finished'
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

// Helper types for easier use
export type Profile = Database['public']['Tables']['profiles']['Row']
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

export type ScaffoldingReport = Database['public']['Tables']['scaffolding_reports']['Row']
export type ScaffoldingReportInsert = Database['public']['Tables']['scaffolding_reports']['Insert']
export type ScaffoldingReportUpdate = Database['public']['Tables']['scaffolding_reports']['Update']
