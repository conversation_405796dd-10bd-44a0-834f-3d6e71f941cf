'use client'

import { useAuth } from '@/contexts/AuthContext'
import AuthForm from '@/components/auth/AuthForm'
import WorkerDashboard from '@/components/worker/WorkerDashboard'
import ManagerDashboard from '@/components/manager/ManagerDashboard'

export default function Home() {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!user || !profile) {
    return <AuthForm />
  }

  if (profile.role === 'manager') {
    return <ManagerDashboard />
  }

  return <WorkerDashboard />
}
