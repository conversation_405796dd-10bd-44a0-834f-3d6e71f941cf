'use client'

import { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User } from '@supabase/supabase-js'
import { createSupabaseClient } from '@/lib/supabase'
import type { Profile } from '@/lib/database.types'

interface AuthContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  error: string | null
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createSupabaseClient()

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const fetchProfile = useCallback(async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching profile:', error.message || error)
        setError(`Failed to load profile: ${error.message}`)
        return null
      }

      return data as Profile
    } catch (error) {
      console.error('Error fetching profile:', error)
      setError('Failed to load profile')
      return null
    }
  }, [supabase])

  const createProfile = useCallback(async (user: User): Promise<Profile | null> => {
    try {
      const profileData = {
        id: user.id,
        email: user.email!,
        name: user.user_metadata?.name || user.email!.split('@')[0],
        role: (user.user_metadata?.role as 'manager' | 'worker') || 'worker'
      }

      const { data, error } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single()

      if (error) {
        console.error('Error creating profile:', error)
        setError(`Failed to create profile: ${error.message}`)
        return null
      }

      return data as Profile
    } catch (error) {
      console.error('Error creating profile:', error)
      setError('Failed to create profile')
      return null
    }
  }, [supabase])

  const refreshProfile = useCallback(async () => {
    if (!user) return

    setError(null)
    let profileData = await fetchProfile(user.id)

    // If profile doesn't exist, create it
    if (!profileData) {
      profileData = await createProfile(user)
    }

    setProfile(profileData)
  }, [user, fetchProfile, createProfile])

  useEffect(() => {
    let mounted = true

    const getSession = async () => {
      try {
        setError(null)
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting session:', error)
          if (mounted) {
            setError(`Session error: ${error.message}`)
            setLoading(false)
          }
          return
        }

        if (mounted) {
          setUser(session?.user ?? null)

          if (session?.user) {
            let profileData = await fetchProfile(session.user.id)
            // If profile doesn't exist, create it
            if (!profileData) {
              profileData = await createProfile(session.user)
            }
            setProfile(profileData)
          } else {
            setProfile(null)
          }

          setLoading(false)
        }
      } catch (error) {
        console.error('Error in getSession:', error)
        if (mounted) {
          setError('Failed to initialize session')
          setLoading(false)
        }
      }
    }

    getSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return

        console.log('Auth state changed:', event, session?.user?.email)

        setUser(session?.user ?? null)
        setError(null)

        if (session?.user) {
          let profileData = await fetchProfile(session.user.id)
          // If profile doesn't exist, create it
          if (!profileData) {
            profileData = await createProfile(session.user)
          }
          setProfile(profileData)
        } else {
          setProfile(null)
        }

        setLoading(false)
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [fetchProfile, createProfile])

  const signOut = useCallback(async () => {
    try {
      setError(null)
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
        setError(`Sign out failed: ${error.message}`)
      } else {
        setUser(null)
        setProfile(null)
      }
    } catch (error) {
      console.error('Error signing out:', error)
      setError('Sign out failed')
    }
  }, [supabase])

  const value = {
    user,
    profile,
    loading,
    error,
    signOut,
    refreshProfile,
    clearError
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
