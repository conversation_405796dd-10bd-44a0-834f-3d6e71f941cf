'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ScaffoldingForm from './ScaffoldingForm'
import ProfileForm from '../profile/ProfileForm'

export default function WorkerDashboard() {
  const { profile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState<'form' | 'profile'>('form')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Scaffolding Management
              </h1>
              <p className="text-sm text-gray-600">Welcome, {profile?.name}</p>
            </div>
            <button
              onClick={signOut}
              className="text-sm text-red-600 hover:text-red-700 font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="px-4">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('form')}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'form'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Submit Report
            </button>
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'profile'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Profile
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'form' && <ScaffoldingForm />}
        {activeTab === 'profile' && <ProfileForm />}
      </div>
    </div>
  )
}
