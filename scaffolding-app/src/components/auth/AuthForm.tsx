'use client'

import { useState } from 'react'
import { createSupabaseClient } from '@/lib/supabase'

interface FormData {
  name?: string
  email: string
  password: string
  confirmPassword?: string
}

interface FormErrors {
  name?: string
  email?: string
  password?: string
  confirmPassword?: string
  general?: string
}

export default function AuthForm() {
  const [isLogin, setIsLogin] = useState(true)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [message, setMessage] = useState('')
  const supabase = createSupabaseClient()

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Email validation
    if (!formData.email?.trim()) {
      newErrors.email = 'Email is required'
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email.trim())) {
        newErrors.email = 'Please enter a valid email address'
      }
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    } else if (formData.password.length > 72) {
      newErrors.password = 'Password must be less than 72 characters'
    }

    // Registration-specific validation
    if (!isLogin) {
      if (!formData.name?.trim()) {
        newErrors.name = 'Full name is required'
      } else if (formData.name.trim().length < 2) {
        newErrors.name = 'Name must be at least 2 characters'
      } else if (formData.name.trim().length > 50) {
        newErrors.name = 'Name must be less than 50 characters'
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password'
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    // Trim whitespace for email and name fields
    const trimmedValue = (field === 'email' || field === 'name') ? value.trim() : value

    setFormData(prev => ({ ...prev, [field]: trimmedValue }))

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // Clear general message when user starts typing
    if (message) {
      setMessage('')
    }

    // Clear general error when user starts typing
    if (errors.general) {
      setErrors(prev => ({ ...prev, general: undefined }))
    }
  }

  const handleLogin = async () => {
    if (!validateForm()) return

    setLoading(true)
    setMessage('')
    setErrors({})

    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: formData.email.trim(),
        password: formData.password
      })

      if (error) {
        // Provide user-friendly error messages
        let errorMessage = error.message
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.'
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Please check your email and click the confirmation link before signing in.'
        } else if (error.message.includes('Too many requests')) {
          errorMessage = 'Too many login attempts. Please wait a moment before trying again.'
        }
        setErrors({ general: errorMessage })
      } else if (authData.user) {
        console.log('Login successful for user:', authData.user.email)
        // AuthContext will handle the redirect
      }
    } catch (error) {
      console.error('Login error:', error)
      setErrors({ general: 'An unexpected error occurred. Please check your internet connection and try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleRegister = async () => {
    if (!validateForm()) return

    setLoading(true)
    setMessage('')
    setErrors({})

    try {
      const { data, error } = await supabase.auth.signUp({
        email: formData.email.trim(),
        password: formData.password,
        options: {
          data: {
            name: formData.name?.trim(),
            role: 'worker' // Default role for self-registration
          }
        }
      })

      if (error) {
        // Provide user-friendly error messages
        let errorMessage = error.message
        if (error.message.includes('User already registered')) {
          errorMessage = 'An account with this email already exists. Please try signing in instead.'
        } else if (error.message.includes('Password should be at least')) {
          errorMessage = 'Password must be at least 6 characters long.'
        } else if (error.message.includes('Invalid email')) {
          errorMessage = 'Please enter a valid email address.'
        } else if (error.message.includes('Signup is disabled')) {
          errorMessage = 'New registrations are currently disabled. Please contact support.'
        }
        setErrors({ general: errorMessage })
      } else if (data.user) {
        if (data.user.email_confirmed_at) {
          setMessage('Registration successful! You can now sign in.')
        } else {
          setMessage('Registration successful! Please check your email and click the confirmation link to activate your account.')
        }
        // Clear form after successful registration
        setFormData({ name: '', email: '', password: '', confirmPassword: '' })
      }
    } catch (error) {
      console.error('Registration error:', error)
      setErrors({ general: 'An unexpected error occurred. Please check your internet connection and try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (isLogin) {
      handleLogin()
    } else {
      handleRegister()
    }
  }

  const switchMode = (loginMode: boolean) => {
    setIsLogin(loginMode)
    setFormData({ name: '', email: '', password: '', confirmPassword: '' })
    setErrors({})
    setMessage('')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Scaffolding Management
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {isLogin ? 'Sign in to your account' : 'Create a new account'}
          </p>
        </div>

        {/* Toggle between Login and Register */}
        <div className="flex rounded-lg shadow-sm bg-gray-100 p-1">
          <button
            type="button"
            onClick={() => switchMode(true)}
            className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-all ${
              isLogin
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Login
          </button>
          <button
            type="button"
            onClick={() => switchMode(false)}
            className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-all ${
              !isLogin
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            Register
          </button>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-5">
            {/* Name field - only show for registration */}
            {!isLogin && (
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name
                </label>
                <input
                  id="name"
                  type="text"
                  value={formData.name || ''}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                    errors.name
                      ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                  } text-gray-900`}
                  placeholder="Enter your full name"
                  autoComplete="name"
                  disabled={loading}
                />
                {errors.name && (
                  <p className="mt-2 text-sm text-red-600">{errors.name}</p>
                )}
              </div>
            )}

            {/* Email field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email address
              </label>
              <input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                  errors.email
                    ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                } text-gray-900`}
                placeholder="Enter your email"
                autoComplete="email"
                disabled={loading}
              />
              {errors.email && (
                <p className="mt-2 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Password field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                  errors.password
                    ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                } text-gray-900`}
                placeholder={isLogin ? "Enter your password" : "Create a password"}
                autoComplete={isLogin ? "current-password" : "new-password"}
                disabled={loading}
              />
              {errors.password && (
                <p className="mt-2 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password field - only show for registration */}
            {!isLogin && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                  Confirm Password
                </label>
                <input
                  id="confirmPassword"
                  type="password"
                  value={formData.confirmPassword || ''}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                    errors.confirmPassword
                      ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                  } text-gray-900`}
                  placeholder="Confirm your password"
                  autoComplete="new-password"
                  disabled={loading}
                />
                {errors.confirmPassword && (
                  <p className="mt-2 text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>
            )}
          </div>

          {/* Error message */}
          {errors.general && (
            <div className="text-sm text-center p-3 rounded-md bg-red-50 text-red-700 border border-red-200">
              {errors.general}
            </div>
          )}

          {/* Success message */}
          {message && (
            <div className="text-sm text-center p-3 rounded-md bg-green-50 text-green-700 border border-green-200">
              {message}
            </div>
          )}

          {/* Submit button */}
          <div className="pt-2">
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (
                isLogin ? 'Signing in...' : 'Creating account...'
              ) : (
                isLogin ? 'Sign in' : 'Create account'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
