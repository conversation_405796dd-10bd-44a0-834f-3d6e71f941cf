'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import SubmissionsList from './SubmissionsList'
import UserManagement from './UserManagement'
import ProfileForm from '../profile/ProfileForm'

export default function ManagerDashboard() {
  const { profile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState<'submissions' | 'users' | 'profile'>('submissions')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Manager Dashboard
              </h1>
              <p className="text-sm text-gray-600">Welcome, {profile?.name}</p>
            </div>
            <button
              onClick={signOut}
              className="text-sm text-red-600 hover:text-red-700 font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="px-4">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('submissions')}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'submissions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Submissions
            </button>
            <button
              onClick={() => setActiveTab('users')}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Users
            </button>
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'profile'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Profile
            </button>
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'submissions' && <SubmissionsList />}
        {activeTab === 'users' && <UserManagement />}
        {activeTab === 'profile' && <ProfileForm />}
      </div>
    </div>
  )
}
